import prisma from "@/app/utils/db";
import { ServiceStatus } from "@/generated/prisma";

export async function getServiceRequestById(id: string) {
  try {
    const serviceRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      include: {
        user: true,
        action: {
          include: {
            orderItem: {
              include: {
                order: true,
                product: true,
              },
            },
          },
        },
        address: true,
        showroom: true,
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
        },
      },
    });

    if (!serviceRequest) return null;

    // Convert Decimal fields to numbers for client components
    const serializedServiceRequest = {
      ...serviceRequest,
      user: {
        id: serviceRequest.user.id,
        firstName: serviceRequest.user.firstName,
        lastName: serviceRequest.user.lastName,
        email: serviceRequest.user.email,
      },
      action: serviceRequest.action ? {
        ...serviceRequest.action,
        orderItem: {
          ...serviceRequest.action.orderItem,
          price: serviceRequest.action.orderItem.price.toNumber(),
          order: {
            id: serviceRequest.action.orderItem.order.id,
            orderNumber: serviceRequest.action.orderItem.order.orderNumber,
            amount: serviceRequest.action.orderItem.order.amount.toNumber(),
            totalAmount: serviceRequest.action.orderItem.order.totalAmount.toNumber(),
          },
          product: {
            id: serviceRequest.action.orderItem.product.id,
            Material_Number: serviceRequest.action.orderItem.product.Material_Number,
            Description_Local: serviceRequest.action.orderItem.product.Description_Local,
            PretAM: serviceRequest.action.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: serviceRequest.action.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      } : null,
    };

    return serializedServiceRequest;
  } catch (error) {
    console.error("Error fetching service request:", error);
    return null;
  }
}

export async function getServiceRequests(limit = 50, status?: ServiceStatus) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: status ? { status } : undefined,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        user: true,
        action: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
      },
    });

    // Convert Decimal fields to numbers for client components
    const serializedServiceRequests = serviceRequests.map(request => ({
      ...request,
      user: {
        id: request.user.id,
        firstName: request.user.firstName,
        lastName: request.user.lastName,
        email: request.user.email,
      },
      action: request.action ? {
        ...request.action,
        orderItem: {
          ...request.action.orderItem,
          price: request.action.orderItem.price.toNumber(),
          product: {
            ...request.action.orderItem.product,
            Description_Local: request.action.orderItem.product.Description_Local,
            PretAM: request.action.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: request.action.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      } : null,
    }));

    return serializedServiceRequests;
  } catch (error) {
    console.error("Error fetching service requests:", error);
    return [];
  }
}

export async function getServiceRequestsByUserId(userId: string) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: { userId },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        action: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
          take: 1,
        },
      },
    });

    return serviceRequests;
  } catch (error) {
    console.error("Error fetching service requests for user:", error);
    return [];
  }
}
