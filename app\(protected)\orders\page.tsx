import { Metadata } from "next";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getOrders } from "@/app/getData/order/getOrders";
import OrderList from "@/app/components/order/OrderList";

export const metadata: Metadata = {
  title: "Order Management",
  description: "View and manage customer orders",
};

interface OrdersPageProps {
  searchParams: Promise<{
    page?: string;
    perPage?: string;
    sort?: string;
    order?: string;
    query?: string;
    status?: string;
  }>;
}

export default async function OrdersRoute({ searchParams }: OrdersPageProps) {
  await requireAdminOrModerator();

  // Await the searchParams Promise
  const params = await searchParams;

  const page = Number(params.page) || 1;
  const perPage = Number(params.perPage) || 10;
  const sort = params.sort || "createdAt";

  const { orders, totalOrders, totalPages } = await getOrders({
    query: params.query,
    status: params.status as any,
    page,
    perPage,
    sort,
    order: (params.order === "asc" || params.order === "desc") ? params.order : "desc",
  });

  return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Order Management</h1>
        </div>

        <OrderList
          orders={orders}
          totalOrders={totalOrders}
          totalPages={totalPages}
          currentPage={page}
          perPage={perPage}
        />
      </div>
  );
}
