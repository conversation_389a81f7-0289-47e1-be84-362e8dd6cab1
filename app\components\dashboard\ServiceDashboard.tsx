import Link from "next/link";
import { getServiceRequests } from "@/app/getData/service/data";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ServiceStatus } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";

export default async function ServiceDashboard() {
  // Get the 5 most recent service requests
  const recentServiceRequests = await getServiceRequests(5);

  // Count service requests by status
  const requestedServices = await getServiceRequests(100, ServiceStatus.requested);
  const pendingCount = requestedServices.length;

  // Helper function to get status badge color
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ServiceStatus.scheduled:
        return "bg-yellow-100 text-yellow-800";
      case ServiceStatus.inProgress:
        return "bg-orange-100 text-orange-800";
      case ServiceStatus.diagnosisComplete:
        return "bg-purple-100 text-purple-800";
      case ServiceStatus.awaitingParts:
        return "bg-amber-100 text-amber-800";
      case ServiceStatus.awaitingApproval:
        return "bg-indigo-100 text-indigo-800";
      case ServiceStatus.completed:
        return "bg-green-100 text-green-800";
      case ServiceStatus.delivered:
        return "bg-emerald-100 text-emerald-800";
      case ServiceStatus.cancelled:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Services</CardTitle>
        <CardDescription>
          Manage customer service requests
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800">Pending Services</h3>
            <p className="text-2xl font-bold text-blue-900">{pendingCount}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-800">Total Services</h3>
            <p className="text-2xl font-bold text-gray-900">{recentServiceRequests.length}</p>
          </div>
        </div>

        <h3 className="text-sm font-medium mb-3">Recent Service Requests</h3>
        {recentServiceRequests.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No service requests found</p>
        ) : (
          <div className="space-y-3">
            {recentServiceRequests.map((serviceRequest) => (
              <div
                key={serviceRequest.id}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p className="font-medium">{serviceRequest.serviceNumber}</p>
                  <p className="text-sm text-gray-500">
                    {formatDate(serviceRequest.createdAt)}
                  </p>
                </div>
                <Badge className={getStatusColor(serviceRequest.status)}>
                  {serviceRequest.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full">
          <Link href="/services">View All Service Requests</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}