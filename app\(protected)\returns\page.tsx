import { getReturns } from "@/app/getData/return/data";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import ReturnList from "@/app/components/return/ReturnList";

export const metadata = {
  title: "Returns | Parts Database",
  description: "Manage product returns",
};

interface ReturnsPageProps {
  searchParams: Promise<{
    page?: string;
    perPage?: string;
    sort?: string;
    order?: string;
    query?: string;
    status?: string;
  }>;
}

export default async function ReturnsPage({ searchParams }: ReturnsPageProps) {
  await requireAdminOrModerator();

  // Await the searchParams Promise
  const params = await searchParams;

  const page = Number(params.page) || 1;
  const perPage = Number(params.perPage) || 10;
  const sort = params.sort || "createdAt";

  const { returns, totalReturns, totalPages } = await getReturns({
    query: params.query,
    status: params.status as any,
    page,
    perPage,
    sort,
    order: (params.order === "asc" || params.order === "desc") ? params.order : "desc",
  });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Returns</h1>
      </div>

      <ReturnList
        returns={returns}
        totalReturns={totalReturns}
        totalPages={totalPages}
        currentPage={page}
        perPage={perPage}
      />
    </div>
  );
}