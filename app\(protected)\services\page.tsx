import { Metadata } from "next";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getServiceRequests } from "@/app/getData/service/data";
import ServiceList from "@/app/components/service/ServiceList";

export const metadata: Metadata = {
  title: "Service Management",
  description: "View and manage customer service requests",
};

export default async function ServicesRoute() {
  await requireAdminOrModerator();
  const services = await getServiceRequests();

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Service Management</h1>
      </div>

      <ServiceList services={services} />
    </div>
  );
}