"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { ReturnStatus } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";
import { Pagination } from "@/components/ui/pagination";

interface ReturnItem {
  id: string;
  returnNumber: string;
  status: ReturnStatus;
  createdAt: Date;
  refundAmount: number | null;
  order: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
      email: string;
    };
  };
  returnItems: Array<{
    id: string;
    quantity: number;
    orderItem: {
      price: number;
      product: {
        Description_Local: string | null;
      };
    };
  }>;
}

interface ReturnListProps {
  returns: ReturnItem[];
  totalReturns: number;
  totalPages: number;
  currentPage: number;
  perPage: number;
}

export default function ReturnList({ 
  returns, 
  totalReturns, 
  totalPages, 
  currentPage, 
  perPage 
}: ReturnListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get current search and filter parameters
  const currentQuery = searchParams.get("query") || "";
  const currentStatus = searchParams.get("status") || "all";
  const currentSort = searchParams.get("sort") || "createdAt";
  const currentOrder = searchParams.get("order") || "desc";
  
  const [searchTerm, setSearchTerm] = useState(currentQuery);
  const [statusFilter, setStatusFilter] = useState(currentStatus);
  
  // Function to handle search
  const handleSearch = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("query", value);
    } else {
      params.delete("query");
    }
    params.set("page", "1"); // Reset to first page
    router.push(`/returns?${params.toString()}`);
  };
  
  // Function to handle status filter
  const handleStatusFilter = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value && value !== "all") {
      params.set("status", value);
    } else {
      params.delete("status");
    }
    params.set("page", "1"); // Reset to first page
    router.push(`/returns?${params.toString()}`);
  };
  
  // Function to handle sorting
  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams);
    
    // If already sorting by this column, toggle order
    if (currentSort === column) {
      params.set("order", currentOrder === "asc" ? "desc" : "asc");
    } else {
      // Otherwise, sort by this column in ascending order
      params.set("sort", column);
      params.set("order", "asc");
    }
    
    router.push(`/returns?${params.toString()}`);
  };
  
  // Function to handle pagination
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`/returns?${params.toString()}`);
  };
  
  // Function to get sort indicator
  const getSortIndicator = (column: string) => {
    if (currentSort !== column) return null;
    return currentOrder === "asc" ? "↑" : "↓";
  };

  // Helper function to get status badge color
  const getStatusColor = (status: ReturnStatus) => {
    switch (status) {
      case ReturnStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ReturnStatus.approved:
        return "bg-green-100 text-green-800";
      case ReturnStatus.received:
        return "bg-purple-100 text-purple-800";
      case ReturnStatus.inspected:
        return "bg-indigo-100 text-indigo-800";
      case ReturnStatus.awaitingReceipt:
        return "bg-yellow-100 text-yellow-800";
      case ReturnStatus.cancelled:
        return "bg-gray-100 text-gray-800";
      case ReturnStatus.completed:
        return "bg-emerald-100 text-emerald-800";
      case ReturnStatus.rejected:
        return "bg-red-100 text-red-800";
      case ReturnStatus.refundIssued:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      {/* Filters */}
      <div className="p-4 border-b">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search by return number or customer..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                // Debounce search
                setTimeout(() => handleSearch(e.target.value), 300);
              }}
            />
          </div>
          <div className="w-full sm:w-48">
            <Select value={statusFilter} onValueChange={(value) => {
              setStatusFilter(value);
              handleStatusFilter(value);
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {Object.values(ReturnStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {returns.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium text-gray-600 mb-4">No returns found</h2>
          <p className="text-gray-500 mb-6">There are no return requests matching your criteria.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("returnNumber")}
                >
                  Return # {getSortIndicator("returnNumber")}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("createdAt")}
                >
                  Date {getSortIndicator("createdAt")}
                </TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Items</TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("status")}
                >
                  Status {getSortIndicator("status")}
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returns.map((returnItem) => (
                <TableRow key={returnItem.id}>
                  <TableCell className="font-medium">
                    {returnItem.returnNumber}
                  </TableCell>
                  <TableCell>{formatDate(returnItem.createdAt)}</TableCell>
                  <TableCell>
                    {returnItem.order?.user?.firstName} {returnItem.order?.user?.lastName}
                  </TableCell>
                  <TableCell>{returnItem.returnItems.length} items</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(returnItem.status)}>
                      {returnItem.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/returns/${returnItem.id}`}>
                        View Details
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
      
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-4 border-t">
          <div className="text-sm text-gray-500">
            Showing <span className="font-medium">{(currentPage - 1) * perPage + 1}</span> to{" "}
            <span className="font-medium">
              {Math.min(currentPage * perPage, totalReturns)}
            </span>{" "}
            of <span className="font-medium">{totalReturns}</span> returns
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
