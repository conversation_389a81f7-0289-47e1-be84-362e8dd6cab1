import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getFeaturedProducts } from "@/app/getData/product/data";
import FeaturedProductsTable from "@/app/components/featured/FeaturedProductsTable";
import AddFeaturedProductForm from "@/app/components/featured/AddFeaturedProductForm";

export default async function FeaturedRoute() {
  await requireAdminOrModerator();
  
  const featuredProducts = await getFeaturedProducts();
  const featuredCount = featuredProducts.length;

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Featured Products</h1>
          <p className="text-muted-foreground">
            {featuredCount} of 18 products featured ({18 - featuredCount} slots available)
          </p>
        </div>
      </div>
      
      <Tabs defaultValue="featured" className="mb-6">
        <TabsList>
          <TabsTrigger value="featured">Featured Products</TabsTrigger>
          <TabsTrigger value="add-single">Add by Material Number</TabsTrigger>
        </TabsList>
        
        <TabsContent value="featured">
          {featuredProducts.length === 0 ? (
            <div className="text-center py-12 border rounded-lg">
              <h2 className="text-xl font-semibold mb-2">No Featured Products</h2>
              <p className="text-muted-foreground mb-4">
                Add products to be displayed on the landing page.
              </p>
            </div>
          ) : (
            <FeaturedProductsTable products={featuredProducts} />
          )}
        </TabsContent>
        
        <TabsContent value="add-single">
          <Card>
            <CardHeader>
              <CardTitle>Add Featured Product</CardTitle>
              <CardDescription>
                Enter a material number to add a product to the featured list.
                Maximum of 18 products can be featured at once.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AddFeaturedProductForm  />
            </CardContent>
          </Card>
        </TabsContent>
        
      </Tabs>
    </div>
  );
}
