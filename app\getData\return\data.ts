import prisma from "@/app/utils/db";
import { ReturnStatus } from "@/generated/prisma";

export async function getReturnById(id: string) {
  try {
    const returnData = await prisma.return.findUnique({
      where: { id },
      include: {
        order: {
          include: {
            orderItems: {
              select: {
                id: true,
                quantity: true,
                price: true,
                notes: true,
                notesToInvoice: true,
                createdAt: true,
                updatedAt: true,
                product: {
                  select: {
                    id: true,
                    Material_Number: true,
                    Description_Local: true,
                    PretAM: true,
                    FinalPrice: true,
                  },
                },
              },
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        returnItems: {
          include: {
            orderItem: {
              select: {
                id: true,
                quantity: true,
                price: true,
                product: {
                  select: {
                    id: true,
                    Material_Number: true,
                    Description_Local: true,
                    PretAM: true,
                    FinalPrice: true,
                  },
                },
              },
            },
          },
        },
        address: true,
        showroom: true,
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!returnData) return null;

    // Convert Decimal fields to numbers for client components
    const serializedReturnData = {
      ...returnData,
      refundAmount: returnData.refundAmount?.toNumber() || null,
      order: {
        ...returnData.order,
        amount: returnData.order.amount.toNumber(),
        totalAmount: returnData.order.totalAmount.toNumber(),
        shippingCost: returnData.order.shippingCost?.toNumber() || null,
        orderItems: returnData.order.orderItems.map(item => ({
          ...item,
          price: item.price.toNumber(),
          product: {
            ...item.product,
            PretAM: item.product.PretAM?.toNumber() || null,
            FinalPrice: item.product.FinalPrice?.toNumber() || null,
          },
        })),
      },
      returnItems: returnData.returnItems.map(item => ({
        ...item,
        orderItem: {
          ...item.orderItem,
          price: item.orderItem.price.toNumber(),
          product: {
            ...item.orderItem.product,
            PretAM: item.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: item.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      })),
    };

    return serializedReturnData;
  } catch (error) {
    console.error("Error fetching return:", error);
    return null;
  }
}

export async function getReturns(limit = 50, status?: ReturnStatus) {
  try {
    const returns = await prisma.return.findMany({
      where: status ? { status } : undefined,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        order: {
          include: {
            user: true,
          },
        },
        returnItems: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
      },
    });

    // Convert Decimal fields to numbers for client components
    const serializedReturns = returns.map(returnItem => ({
      ...returnItem,
      refundAmount: returnItem.refundAmount?.toNumber() || null,
      order: {
        id: returnItem.order.id,
        user: {
          firstName: returnItem.order.user.firstName,
          lastName: returnItem.order.user.lastName,
          email: returnItem.order.user.email,
        },
      },
      returnItems: returnItem.returnItems.map(item => ({
        ...item,
        orderItem: {
          ...item.orderItem,
          price: item.orderItem.price.toNumber(),
          product: {
            ...item.orderItem.product,
            Description_Local: item.orderItem.product.Description_Local,
            PretAM: item.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: item.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      })),
    }));

    return serializedReturns;
  } catch (error) {
    console.error("Error fetching returns:", error);
    return [];
  }
}

export async function getReturnsByOrderId(orderId: string) {
  try {
    const returns = await prisma.return.findMany({
      where: { orderId },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        returnItems: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
    });

    // Convert Decimal fields to numbers for client components
    const serializedReturns = returns.map(returnItem => ({
      ...returnItem,
      refundAmount: returnItem.refundAmount?.toNumber() || null,
      returnItems: returnItem.returnItems.map(item => ({
        ...item,
        orderItem: {
          id: item.orderItem.id,
          quantity: item.orderItem.quantity,
          price: item.orderItem.price.toNumber(),
          product: {
            id: item.orderItem.product.id,
            Material_Number: item.orderItem.product.Material_Number,
            Description_Local: item.orderItem.product.Description_Local,
            PretAM: item.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: item.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      })),
    }));

    return serializedReturns;
  } catch (error) {
    console.error("Error fetching returns for order:", error);
    return [];
  }
}