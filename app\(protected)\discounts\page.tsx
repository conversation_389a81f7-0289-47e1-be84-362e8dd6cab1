import Link from "next/link";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { redirect } from "next/navigation";
import { handleActivateDiscounts, handleExpiredDiscounts } from "@/app/actions/actions";
import prisma from "@/app/utils/db";

// Wrapper functions for form actions
async function processExpiredDiscounts() {
  await handleExpiredDiscounts();
}

async function activateDiscounts() {
  await handleActivateDiscounts();
}

async function getDiscounts() {
    const discounts = await prisma.discount.findMany({
        orderBy: {
            createdAt: "desc",
        },
    });

    return discounts;
}

async function getTotalProductsForADiscount(discountId: string): Promise<number> {
  if (!discountId) {
    throw new Error("Invalid discount ID");
  }

  try {
    const totalProducts = await prisma.productDiscount.count({
      where: {
        discountId,
      },
    });

    return totalProducts;
  } catch (error) {
    console.error("Error fetching total products for discount:", error);
    throw new Error("Failed to fetch total products for discount");
  }
}
    
export default async function DiscountsRoute(){

    const user = await requireAdminOrModerator();

    if(!user){
        return redirect("/unauthorized");
    }

    const discounts = await getDiscounts();

    if (!discounts || discounts.length === 0) {
        return (
            <div className="p-4">
                <h1 className="text-2xl font-bold">Discounts</h1>
                <p className="my-4">No discounts found.</p>
                <Link href="/discounts/create" className="underline">
                    <Button variant="default">Create Discount</Button>
                </Link>
            </div>
        );
    }

    return (
        <>

            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">All Discounts</h2>

                <div className="flex gap-4">
                    <Link href="/discounts/create">
                        <Button variant="default">Create Discount</Button>
                    </Link>

                    <form action={processExpiredDiscounts}>
                        <Button type="submit" variant="outline">Process Expired Discounts</Button>
                    </form>

                    <form action={activateDiscounts}>
                        <Button type="submit" variant="outline">Activate Discounts</Button>
                    </form>
                </div>
            </div>

            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead>Active</TableHead>
                        <TableHead>Created By</TableHead>
                        <TableHead>Created At</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Total Products</TableHead>
                        <TableHead>View</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>History</TableHead>
                    </TableRow>
                </TableHeader>

                <TableBody>
                    {discounts.map((discount) => (
                        <TableRow key={discount.id}>
                            <TableCell className="font-medium">{discount.name}</TableCell>
                            <TableCell>{discount.description}</TableCell>
                            <TableCell>{discount.type}</TableCell>
                            <TableCell>{discount.value.toString()}</TableCell>
                            <TableCell>{discount.active ? "Yes" : "No"}</TableCell>
                            <TableCell>{discount.createdBy}</TableCell>
                            <TableCell>{discount.createdAt ? discount.createdAt.toLocaleString() : "N/A"}</TableCell>
                            <TableCell>
                                {discount.startDate ? discount.startDate.toLocaleString() : "N/A"}
                            </TableCell>
                            <TableCell>
                                {discount.endDate ? discount.endDate.toLocaleString() : "N/A"}
                            </TableCell>
                            <TableCell>
                                {getTotalProductsForADiscount(discount.id)
                                }
                                </TableCell>
                            <TableCell>
                                <Link href={`/discounts/products/${discount.id}`} className="underline">
                                    View Products
                                </Link>
                            </TableCell>
                            <TableCell>
                                <Link href={`/discounts/update/${discount.id}`} className="underline">
                                    Edit
                                </Link>    
                            </TableCell>
                            <TableCell>
                                <Link href={`/discounts/history/${discount.id}`} className="underline">
                                    History
                                </Link> 
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </>
    )
}
