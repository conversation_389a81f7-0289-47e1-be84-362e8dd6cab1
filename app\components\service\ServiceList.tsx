"use client";

import { useState } from "react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ServiceStatus } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";

interface ServiceRequest {
  id: string;
  serviceNumber: string;
  status: ServiceStatus;
  issueType: string;
  description: string;
  createdAt: Date;
  user: {
    firstName: string;
    lastName: string;
    email: string;
  };
  action?: {
    orderItem: {
      product: {
        Description_Local: string | null;
      };
    };
  } | null;
}

interface ServiceListProps {
  services: ServiceRequest[];
}

export default function ServiceList({ services }: ServiceListProps) {
  const [filteredServices, setFilteredServices] = useState(services);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<ServiceStatus | "all">("all");

  // Filter services based on search term and status
  const handleFilter = () => {
    let filtered = services;

    if (searchTerm) {
      filtered = filtered.filter(
        (service) =>
          service.serviceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          service.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          service.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          service.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          service.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((service) => service.status === statusFilter);
    }

    setFilteredServices(filtered);
  };

  // Helper function to get status badge color
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ServiceStatus.scheduled:
        return "bg-yellow-100 text-yellow-800";
      case ServiceStatus.inProgress:
        return "bg-orange-100 text-orange-800";
      case ServiceStatus.diagnosisComplete:
        return "bg-purple-100 text-purple-800";
      case ServiceStatus.awaitingParts:
        return "bg-amber-100 text-amber-800";
      case ServiceStatus.awaitingApproval:
        return "bg-indigo-100 text-indigo-800";
      case ServiceStatus.completed:
        return "bg-green-100 text-green-800";
      case ServiceStatus.delivered:
        return "bg-emerald-100 text-emerald-800";
      case ServiceStatus.cancelled:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Service Requests ({filteredServices.length})</CardTitle>
        
        {/* Filters */}
        <div className="flex gap-4 mt-4">
          <Input
            placeholder="Search by service number, customer, or description..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setTimeout(handleFilter, 300);
            }}
            className="max-w-md"
          />
          
          <Select
            value={statusFilter}
            onValueChange={(value: ServiceStatus | "all") => {
              setStatusFilter(value);
              setTimeout(handleFilter, 100);
            }}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {Object.values(ServiceStatus).map((status) => (
                <SelectItem key={status} value={status}>
                  {status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button onClick={handleFilter} variant="outline">
            Apply Filters
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {filteredServices.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No service requests found.</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service Number</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Issue Type</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredServices.map((service) => (
                <TableRow key={service.id}>
                  <TableCell className="font-medium">
                    {service.serviceNumber}
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">
                        {service.user.firstName} {service.user.lastName}
                      </p>
                      <p className="text-sm text-gray-500">{service.user.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    {service.issueType}
                  </TableCell>
                  <TableCell>
                    {service.action?.orderItem.product.Description_Local || "N/A"}
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(service.status)}>
                      {service.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(service.createdAt)}</TableCell>
                  <TableCell>
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/services/${service.id}`}>
                        View Details
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
