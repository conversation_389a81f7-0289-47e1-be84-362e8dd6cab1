"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ServiceStatus } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";
import { Pagination } from "@/components/ui/pagination";

interface ServiceRequest {
  id: string;
  serviceNumber: string;
  status: ServiceStatus;
  issueType: string;
  description: string;
  createdAt: Date;
  user: {
    firstName: string;
    lastName: string;
    email: string;
  };
  action?: {
    orderItem: {
      product: {
        Description_Local: string | null;
      };
    };
  } | null;
}

interface ServiceListProps {
  services: ServiceRequest[];
  totalServices: number;
  totalPages: number;
  currentPage: number;
  perPage: number;
}

export default function ServiceList({
  services,
  totalServices,
  totalPages,
  currentPage,
  perPage
}: ServiceListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current search and filter parameters
  const currentQuery = searchParams.get("query") || "";
  const currentStatus = searchParams.get("status") || "all";
  const currentSort = searchParams.get("sort") || "createdAt";
  const currentOrder = searchParams.get("order") || "desc";

  const [searchTerm, setSearchTerm] = useState(currentQuery);
  const [statusFilter, setStatusFilter] = useState<ServiceStatus | "all">(currentStatus as ServiceStatus | "all");

  // Function to handle search
  const handleSearch = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("query", value);
    } else {
      params.delete("query");
    }
    params.set("page", "1"); // Reset to first page
    router.push(`/services?${params.toString()}`);
  };

  // Function to handle status filter
  const handleStatusFilter = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value && value !== "all") {
      params.set("status", value);
    } else {
      params.delete("status");
    }
    params.set("page", "1"); // Reset to first page
    router.push(`/services?${params.toString()}`);
  };

  // Function to handle sorting
  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams);

    // If already sorting by this column, toggle order
    if (currentSort === column) {
      params.set("order", currentOrder === "asc" ? "desc" : "asc");
    } else {
      // Otherwise, sort by this column in ascending order
      params.set("sort", column);
      params.set("order", "asc");
    }

    router.push(`/services?${params.toString()}`);
  };

  // Function to handle pagination
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`/services?${params.toString()}`);
  };

  // Function to get sort indicator
  const getSortIndicator = (column: string) => {
    if (currentSort !== column) return null;
    return currentOrder === "asc" ? "↑" : "↓";
  };

  // Helper function to get status badge color
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ServiceStatus.scheduled:
        return "bg-yellow-100 text-yellow-800";
      case ServiceStatus.inProgress:
        return "bg-orange-100 text-orange-800";
      case ServiceStatus.diagnosisComplete:
        return "bg-purple-100 text-purple-800";
      case ServiceStatus.awaitingParts:
        return "bg-amber-100 text-amber-800";
      case ServiceStatus.awaitingApproval:
        return "bg-indigo-100 text-indigo-800";
      case ServiceStatus.completed:
        return "bg-green-100 text-green-800";
      case ServiceStatus.delivered:
        return "bg-emerald-100 text-emerald-800";
      case ServiceStatus.cancelled:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Service Requests ({totalServices})</CardTitle>

        {/* Filters */}
        <div className="flex gap-4 mt-4">
          <Input
            placeholder="Search by service number, customer, or description..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              // Debounce search
              setTimeout(() => handleSearch(e.target.value), 300);
            }}
            className="max-w-md"
          />

          <Select
            value={statusFilter}
            onValueChange={(value: ServiceStatus | "all") => {
              setStatusFilter(value);
              handleStatusFilter(value);
            }}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {Object.values(ServiceStatus).map((status) => (
                <SelectItem key={status} value={status}>
                  {status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {services.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No service requests found.</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort("serviceNumber")}
                >
                  Service Number {getSortIndicator("serviceNumber")}
                </TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Issue Type</TableHead>
                <TableHead>Product</TableHead>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort("status")}
                >
                  Status {getSortIndicator("status")}
                </TableHead>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort("createdAt")}
                >
                  Created {getSortIndicator("createdAt")}
                </TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {services.map((service) => (
                <TableRow key={service.id}>
                  <TableCell className="font-medium">
                    {service.serviceNumber}
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">
                        {service.user.firstName} {service.user.lastName}
                      </p>
                      <p className="text-sm text-gray-500">{service.user.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    {service.issueType}
                  </TableCell>
                  <TableCell>
                    {service.action?.orderItem.product.Description_Local || "N/A"}
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(service.status)}>
                      {service.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(service.createdAt)}</TableCell>
                  <TableCell>
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/services/${service.id}`}>
                        View Details
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {totalPages > 1 && (
          <div className="flex items-center justify-between px-4 py-4 border-t">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">{(currentPage - 1) * perPage + 1}</span> to{" "}
              <span className="font-medium">
                {Math.min(currentPage * perPage, totalServices)}
              </span>{" "}
              of <span className="font-medium">{totalServices}</span> service requests
            </div>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
