{"level":"error","message":"Failed to add phone as email in Clerk: [<PERSON><PERSON><PERSON>]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 65dc9143ce0ad818850ce912885aeda4 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:19:10"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 68a026a80a9a29a5be599d67543d3564 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:23:58"}
{"level":"error","message":"Failed to add phone as email in Clerk: [Error]\nMessage:Forbidden\nStatus:403\nSerialized errors: {\"code\":\"feature_not_enabled\",\"message\":\"not enabled\",\"longMessage\":\"This feature is not enabled on this instance\",\"meta\":{}}\nClerk Trace ID: 4318ffd6cbeb4d4be0c0624b300c9c75 for user cmelwhaxx00001ghx4mmv9<NAME_EMAIL>","timestamp":"2025-09-22 11:25:51"}
