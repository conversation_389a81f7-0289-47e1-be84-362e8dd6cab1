import { Metadata } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { deleteBanner } from "@/app/actions/bannerActions";
import { getBanners } from "@/app/getData/banner/getBanners";
import BannerList from "@/app/components/banner/BannerList";

export const metadata: Metadata = {
  title: "Banner Management",
  description: "Manage website banners and promotions",
};

export default async function BannerRoute() {

  await requireAdminOrModerator();

  const banners = await getBanners();

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Banner Management</h1>
        <Link href="/banner/create">
          <Button>Create New Banner</Button>
        </Link>
      </div>
      
      <BannerList banners={banners} deleteBanner={deleteBanner} />
    </div>
  );
}
